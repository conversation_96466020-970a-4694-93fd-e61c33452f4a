# DermaCare Data Sync System - Project Analysis & Documentation

## Executive Summary

The DermaCare Data Sync System is a sophisticated bidirectional data synchronization platform that enables seamless integration between two healthcare management systems:

- **CC (CliniCore)**: A comprehensive clinic management system for patient records, appointments, invoicing, and payments
- **AP (AutoPatient/GoHighLevel)**: A customer relationship management and marketing automation platform

The system ensures real-time synchronization of patient data, appointments, custom fields, and financial information between both platforms, maintaining data consistency and enabling unified patient management workflows.

## Project Goals & Business Objectives

### Primary Business Objectives

1. **Unified Patient Management**: Maintain synchronized patient records across both platforms to eliminate data silos
2. **Automated Workflow Integration**: Enable seamless appointment booking and management across systems
3. **Financial Data Consolidation**: Sync invoicing and payment data to provide comprehensive financial insights
4. **Custom Field Synchronization**: Maintain consistent custom field data for enhanced patient profiling
5. **Real-time Data Consistency**: Ensure immediate propagation of changes between systems

### Key Features & Functionality

1. **Patient/Contact Synchronization**

   - Bidirectional sync of patient demographics (name, email, phone, DOB, gender)
   - Automatic contact creation and updates
   - Duplicate prevention and conflict resolution

2. **Appointment Management**

   - Real-time appointment creation, updates, and cancellations
   - Status synchronization (confirmed, cancelled, no-show)
   - Service and resource mapping between platforms

3. **Custom Field Mapping**

   - Dynamic custom field creation and synchronization
   - Field value transformation and validation
   - Appointment-specific custom fields (services, practitioners, locations)

4. **Financial Data Integration**

   - Invoice synchronization from CC to AP custom fields
   - Payment tracking and LTV (Lifetime Value) calculations
   - Financial reporting data consolidation

5. **OAuth Authentication**
   - Secure API access management
   - Automatic token refresh mechanisms
   - Multi-location support

## Technical Architecture

### Technology Stack

- **Runtime Environment**: Node.js with AdonisJS framework (old), migrating to Cloudflare Workers
- **Database**: PostgreSQL with Drizzle ORM
- **Queue System**: Bull (Redis-based) for background job processing
- **Real-time Communication**: Socket.io for CC platform events
- **API Integration**: RESTful APIs with OAuth 2.0 authentication
- **Language**: TypeScript for type safety and better developer experience

### Core Architecture Components

#### 1. **Entry Points & Configuration** (`/old/start/`)

- **`kernel.ts`**: Middleware registration and server setup
- **`routes.ts`**: HTTP route definitions for webhooks and OAuth callbacks
- **`bull.ts`**: Background job queue initialization and monitoring UI
- **`jobs.ts`**: Job registration for asynchronous processing
- **`Socket.ts`**: Real-time socket connection to CC platform for live events

#### 2. **HTTP Controllers** (`/old/Http/`)

- **`OAuthsController.ts`**: Handles OAuth flow for AP platform authentication
- **`ProcessApAppointmentCreatesController.ts`**: Processes AP appointment creation webhooks
- **`ProcessApAppointmentUpdatesController.ts`**: Handles AP appointment update webhooks
- **`ProcessApAppointmentDeletesController.ts`**: Manages AP appointment deletion webhooks
- **`SyncServicesController.ts`**: Service synchronization endpoints

#### 3. **Background Job Processors** (`/old/Jobs/`)

- **`ProcessPatientCreate.ts`**: Creates patients in AP from CC events
- **`ProcessPatientUpdate.ts`**: Updates patient information across platforms
- **`ProcessCcAppointmentCreate.ts`**: Creates appointments in AP from CC
- **`ProcessCcAppointmentUpdate.ts`**: Updates appointment information
- **`ProcessCcAppointmentDelete.ts`**: Handles appointment deletions
- **`ProcessInvoicePayment.ts`**: Syncs financial data from CC to AP

#### 4. **Business Logic Layer** (`/old/helpers/`)

**AP Platform Logic (`ap.ts` - 704 lines):**

- Contact creation and updates in AP
- Appointment synchronization to AP
- Custom field mapping and creation
- Financial data synchronization (invoices, payments, LTV)
- Service appointment counting and spending calculations

**CC Platform Logic (`cc.ts` - 453 lines):**

- Patient creation and updates in CC
- Appointment synchronization to CC
- Custom field mapping from AP to CC
- Patient search and duplicate handling

#### 5. **API Request Layer** (`/old/request/`)

- **`request.ts`**: Base HTTP client with OAuth token management and retry logic
- **`ap.ts`**: AP platform-specific API methods (contacts, appointments, custom fields, notes)
- **`cc.ts`**: CC platform-specific API methods (patients, appointments, services, resources)

### Design Patterns & Architectural Decisions

#### 1. **Event-Driven Architecture**

```typescript
// Real-time event processing from CC platform
socket.on("mobimed:App\\Events\\EntityWasCreated", async (data) => {
  switch (data.type) {
    case "patient":
      Bull.add("ProcessPatientCreate", {
        payload: data.payload,
        auth: auth.id,
      });
      break;
    case "appointment":
      Bull.add("ProcessCcAppointmentCreate", {
        payload: data.payload,
        auth: auth.id,
      });
      break;
  }
});
```

#### 2. **OAuth Token Management**

```typescript
// Automatic token refresh with request queuing
if (auth.tokenExpire < DateTime.now().minus({ minutes: 5 })) {
  await refreshAPToken(auth);
}
```

#### 3. **Duplicate Prevention**

```typescript
// Skip logic to prevent processing loops
if (await Skip.hasProcessPatientCreate(contact.apId)) {
  return `We did this creation recently, dropping it.`;
}
```

## Data Flow Documentation

### Input Sources & Data Ingestion

#### 1. **CC Platform Events** (Real-time via Socket.io)

- **Patient Events**: Creation, updates from CC system
- **Appointment Events**: Creation, updates, deletions
- **Invoice/Payment Events**: Financial data changes

#### 2. **AP Platform Webhooks** (HTTP endpoints)

- **Appointment Events**: Creation, updates from AP calendar system
- **Contact Events**: Updates from AP CRM

#### 3. **OAuth Callbacks**

- **Authentication Flow**: Initial setup and token refresh

### Data Transformation Processes

#### 1. **Patient/Contact Mapping**

```typescript
// CC to AP transformation
const payload: PostAPContactType = {
  email: contact.email,
  phone: contact.phone,
  firstName: contact.ccData.firstName,
  lastName: contact.ccData.lastName,
  tags: ["cc_api"],
  dateOfBirth: contact.ccData.dob,
  gender: contact.ccData.gender,
};
```

#### 2. **Appointment Synchronization**

```typescript
// Appointment status mapping
appointmentStatus: appointment.ccData.canceledAt ? "cancelled" : "confirmed";
```

#### 3. **Custom Field Transformation**

- Dynamic field creation if not exists
- Value type conversion and validation
- Field name mapping between platforms

### Storage Mechanisms & Database Interactions

#### Database Schema (PostgreSQL)

```sql
-- Patients table for contact synchronization
CREATE TABLE "patients" (
  "id" varchar(255) PRIMARY KEY,
  "ap_id" varchar(255) UNIQUE,
  "cc_id" integer UNIQUE,
  "email" varchar(255),
  "phone" varchar(255),
  "ap_updated_at" timestamp,
  "cc_updated_at" timestamp,
  "ap_data" jsonb,
  "cc_data" jsonb
);

-- Appointments table for appointment synchronization
CREATE TABLE "appointments" (
  "id" varchar(255) PRIMARY KEY,
  "ap_id" varchar(255) UNIQUE,
  "cc_id" integer UNIQUE,
  "patient_id" varchar(255) REFERENCES patients(id),
  "ap_updated_at" timestamp,
  "cc_updated_at" timestamp,
  "ap_data" jsonb,
  "cc_data" jsonb,
  "ap_note_id" text
);
```

### Output Destinations & API Endpoints

#### 1. **AP Platform APIs**

- **Contacts API**: Patient data synchronization
- **Appointments API**: Calendar event management
- **Custom Fields API**: Dynamic field creation and updates
- **Notes API**: Appointment-related notes

#### 2. **CC Platform APIs**

- **Patients API**: Patient record management
- **Appointments API**: Appointment scheduling
- **Custom Fields API**: Patient custom field updates

## Integration Points with External Systems

### 1. **GoHighLevel (AP Platform)**

- **Base URL**: `https://services.leadconnectorhq.com`
- **Authentication**: OAuth 2.0 with automatic token refresh
- **Rate Limiting**: Handled with request queuing
- **API Version**: 2021-04-15

### 2. **CliniCore (CC Platform)**

- **Base URL**: Configurable per client
- **Authentication**: Bearer token
- **Real-time Events**: Socket.io connection
- **WebSocket URL**: Client-specific socket endpoints

### 3. **Supporting Services**

- **Redis**: Bull queue management and caching
- **PostgreSQL**: Primary data storage
- **Slack**: Error notifications and logging

## Target Users & System Interactions

### 1. **Healthcare Practitioners**

- **Use Case**: Unified patient management across platforms
- **Interaction**: Automatic data sync ensures consistent patient records

### 2. **Administrative Staff**

- **Use Case**: Appointment scheduling and management
- **Interaction**: Cross-platform appointment synchronization

### 3. **Marketing Teams**

- **Use Case**: Patient engagement and communication
- **Interaction**: Synchronized contact data in AP for campaigns

### 4. **Financial Teams**

- **Use Case**: Revenue tracking and patient LTV analysis
- **Interaction**: Automated financial data synchronization

## Migration to Modern Architecture

The system is being migrated from AdonisJS to Cloudflare Workers with Hono framework:

- **Event Processing**: Socket.io → Webhook-based HTTP endpoints
- **Queue System**: Bull/Redis → Cloudflare Queues or KV storage
- **Database**: Maintained PostgreSQL with Drizzle ORM
- **Authentication**: Enhanced OAuth flow with better token management
- **Error Handling**: Centralized error logging to database

This migration maintains all core business logic while improving scalability, performance, and maintainability.

## Key Code Examples & Implementation Patterns

### 1. **Real-time Event Processing**

<augment_code_snippet path="old/start/Socket.ts" mode="EXCERPT">

```typescript
socket.on(
  "mobimed:App\\Events\\EntityWasCreated",
  async (data: SocketEventType) => {
    switch (data.type) {
      case "patient":
        Bull.add("ProcessPatientCreate", {
          payload: data.payload,
          auth: auth.id,
        });
        break;
      case "invoice":
      case "payment":
        Bull.add("ProcessInvoicePayment", {
          payload: data.payload,
          auth: auth.id,
        });
        break;
    }
  }
);
```

</augment_code_snippet>

### 2. **OAuth Token Management with Automatic Refresh**

<augment_code_snippet path="old/request/request.ts" mode="EXCERPT">

```typescript
if (auth.tokenExpire < DateTime.now().minus({ minutes: 5 })) {
  await refreshAPToken(auth);
}

const v2 = axios.create({
  baseURL: "https://services.leadconnectorhq.com",
  headers: {
    Authorization: `Bearer ${auth.APAccessToken}`,
    "Content-Type": "application/json",
    Version: "2021-04-15",
  },
});
```

</augment_code_snippet>

### 3. **Bidirectional Contact Synchronization**

<augment_code_snippet path="old/helpers/ap.ts" mode="EXCERPT">

```typescript
export const updateOrCreateContact = async (
  contact: Contact,
  syncCustomfields = true,
): Promise<Contact> => {
  const payload: PostAPContactType = {
    email: contact.email,
    phone: contact.phone,
    firstName: contact.ccData.firstName,
    lastName: contact.ccData.lastName,
    tags: ["cc_api"],
    dateOfBirth: contact.ccData.dob,
  };

  let apContact: GetAPContactType;
  if (!contact.apId) {
    payload.source = "cc";
    payload.gender = contact.ccData.gender;
    apContact = await contactReq.upsert(payload);
  } else {
    apContact = await contactReq.update(contact.apId, payload);
  }
```

</augment_code_snippet>

### 4. **Custom Field Synchronization**

<augment_code_snippet path="old/helpers/ap.ts" mode="EXCERPT">

```typescript
export const syncCCtoAPCustomfields = async (
  contact: Contact,
  extraFields: any = null
) => {
  let ccNameValue = await getCCCustomfieldsLabelValue(contact);
  if (extraFields) {
    ccNameValue = { ...ccNameValue, ...extraFields };
  }

  const apNameId = await getApCustomfieldsNameId();
  const payload: PostAPContactType = {
    customFields: [],
  };

  for (const key in ccNameValue) {
    if (key in apNameId && payload.customFields) {
      payload.customFields.push({
        id: apNameId[key],
        value: ccNameValue[key],
      });
    } else {
      // Create custom field if it doesn't exist
      const cfRes = await apCustomfield.create({
        name: key,
        dataType: "TEXT",
      });
    }
  }
};
```

</augment_code_snippet>

### 5. **Appointment Synchronization with Status Mapping**

<augment_code_snippet path="old/helpers/ap.ts" mode="EXCERPT">

```typescript
export const createAppointmentToAP = async (appointment: Appointment) => {
  const payload: any = {
    contactId: contact.apId,
    startTime: appointment.startAt.toISO() as string,
    endTime: appointment.endAt.toISO() as string,
    appointmentStatus: appointment.ccData.canceledAt
      ? "cancelled"
      : "confirmed",
  };

  if (appointment.ccData.title) {
    if (appointment.ccData.firstOfPatient) {
      payload.title = `Neukunde: ${removeHtmlTags(appointment.ccData.title)}`;
    } else {
      payload.title = `Bestandskunde: ${removeHtmlTags(
        appointment.ccData.title
      )}`;
    }
  }

  const apRes = await apAppointmentReq.post(payload);
};
```

</augment_code_snippet>

## Data Synchronization Workflows

### Patient Creation Workflow

1. **CC Event**: New patient created in CliniCore
2. **Socket Event**: Real-time notification via Socket.io
3. **Job Queue**: `ProcessPatientCreate` job added to Bull queue
4. **Processing**:
   - Check for existing patient in local database
   - Create/update contact in AP platform
   - Sync custom fields
   - Update local database with AP ID

### Appointment Synchronization Workflow

1. **Trigger**: Appointment created/updated in either platform
2. **Validation**: Check for duplicate processing using Skip logic
3. **Contact Resolution**: Ensure patient/contact exists in target platform
4. **Appointment Sync**: Create/update appointment with proper status mapping
5. **Custom Fields**: Sync appointment-specific custom fields
6. **Notes**: Create appointment notes in AP platform

### Financial Data Sync Workflow

1. **Invoice/Payment Event**: Financial data changes in CC
2. **Data Aggregation**: Calculate LTV, latest payments, invoice totals
3. **Custom Field Updates**: Update AP contact custom fields with financial data
4. **Reporting**: Enable financial reporting in AP platform

## Error Handling & Monitoring

### Skip Logic for Duplicate Prevention

- Prevents processing loops between platforms
- Time-based duplicate detection
- Operation-specific skip tracking

### Error Logging & Notifications

- Centralized error logging to database
- Slack notifications for critical errors
- Request retry mechanisms with exponential backoff

### Monitoring & Observability

- Bull queue monitoring UI (development mode)
- Request/response logging
- Performance metrics tracking

## Security Considerations

### Authentication & Authorization

- OAuth 2.0 flow for AP platform
- Bearer token authentication for CC platform
- Automatic token refresh with secure storage

### Data Protection

- Sensitive data encryption in transit
- JSONB storage for flexible data structures
- Audit trails for all synchronization operations

### API Security

- Rate limiting and request throttling
- Input validation and sanitization
- Error message sanitization to prevent information leakage
