import { HTTPException } from "hono/http-exception";
import type { ContentfulStatusCode } from "hono/utils/http-status";

const createError = (
	messageOrOptions:
		| string
		| {
				message: string;
				status: ContentfulStatusCode;
				cause?: any;
				res?: Response;
		  },
	status: ContentfulStatusCode = 500,
) => {
	if (typeof messageOrOptions === "string") {
		return new HTTPException(status, { message: messageOrOptions });
	}
	const { message, cause, res, status: statusCode } = messageOrOptions;
	return new HTTPException(statusCode || status, {
		message,
		cause,
		res,
	});
};

export default createError;
