import { cleanData, getConfigs } from "@utils";

/**
 * Request options interface
 */
export interface RequestOptions {
	method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
	path: string;
	data?: any;
	params?: Record<string, any>;
	headers?: Record<string, string>;
	cacheKey?: string;
	cacheTTL?: number;
	retries?: number;
}

/**
 * API Response interface
 */
export interface ApiResponse<T = any> {
	data: T;
	status: number;
	headers: Headers;
	cached: boolean;
}

/**
 * Error types for better error handling
 */
export class ApiError extends Error {
	constructor(
		message: string,
		public status: number,
		public code?: string,
		public response?: any,
	) {
		super(message);
		this.name = "ApiError";
	}
}

/**
 * Optimized Request Manager with caching, retry logic, and connection pooling
 */
export class RequestManager {
	private readonly baseUrl: string;
	private readonly apiKey: string;
	private readonly config = getConfigs();
	private readonly cache = new Map<
		string,
		{ data: any; timestamp: number; ttl: number }
	>();
	private readonly pendingRequests = new Map<string, Promise<any>>();

	constructor(baseUrl: string, apiKey: string) {
		this.baseUrl = baseUrl;
		this.apiKey = apiKey;
	}

	/**
	 * Make an API request with caching, retry logic, and deduplication
	 * @param options - Request configuration
	 * @returns Promise with API response
	 */
	async request<T = any>(options: RequestOptions): Promise<ApiResponse<T>> {
		const {
			method,
			path,
			data,
			params,
			headers = {},
			cacheKey,
			cacheTTL = this.config.cacheTTL,
			retries = this.config.maxRetries,
		} = options;

		// Check cache for GET requests
		if (method === "GET" && cacheKey) {
			const cached = this.getFromCache(cacheKey);
			if (cached) {
				return {
					data: cached,
					status: 200,
					headers: new Headers(),
					cached: true,
				};
			}
		}

		// Create request deduplication key
		const requestKey = this.createRequestKey(method, path, params, data);

		// Check if request is already pending
		if (this.pendingRequests.has(requestKey)) {
			const result = await this.pendingRequests.get(requestKey)!;
			return {
				data: result,
				status: 200,
				headers: new Headers(),
				cached: false,
			};
		}

		// Execute request with retry logic
		const requestPromise = this.executeRequestWithRetry<T>({
			method,
			path,
			data,
			params,
			headers,
			retries,
		});

		this.pendingRequests.set(requestKey, requestPromise);

		try {
			const result = await requestPromise;

			// Cache successful GET responses
			if (method === "GET" && cacheKey) {
				this.setCache(cacheKey, result, cacheTTL);
			}

			return {
				data: result,
				status: 200,
				headers: new Headers(),
				cached: false,
			};
		} finally {
			this.pendingRequests.delete(requestKey);
		}
	}

	/**
	 * Execute request with exponential backoff retry logic
	 * @param options - Request options
	 * @param attempt - Current attempt number
	 * @returns Promise with response data
	 */
	private async executeRequestWithRetry<T>(
		options: Omit<RequestOptions, "cacheKey" | "cacheTTL">,
		attempt = 1,
	): Promise<T> {
		try {
			return await this.executeRequest<T>(options);
		} catch (error) {
			if (attempt <= options.retries! && this.isRetryableError(error)) {
				const delay = 2 ** (attempt - 1) * 1000; // Exponential backoff
				await this.sleep(delay);
				return this.executeRequestWithRetry<T>(options, attempt + 1);
			}
			throw error;
		}
	}

	/**
	 * Execute a single API request
	 * @param options - Request options
	 * @returns Promise with response data
	 */
	private async executeRequest<T>(
		options: Omit<RequestOptions, "cacheKey" | "cacheTTL">,
	): Promise<T> {
		const { method, path, data, params, headers } = options;
		// Build URL with query parameters
		const url = new URL(path, this.baseUrl);
		if (params) {
			Object.entries(cleanData(params)).forEach(([key, value]) => {
				if (Array.isArray(value)) {
					value.forEach((v) => url.searchParams.append(`${key}[]`, String(v)));
				} else {
					url.searchParams.append(key, String(value));
				}
			});
		}
		// Prepare request body
		const body = data ? JSON.stringify(cleanData(data)) : undefined;
		// Make request
		const response = await fetch(url.toString(), {
			method,
			headers: {
				Authorization: `Bearer ${this.apiKey}`,
				"Content-Type": "application/json",
				Accept: "application/json",
				...headers,
			},
			body,
			signal: AbortSignal.timeout(this.config.requestTimeout),
		});
		if (!response.ok) {
			let errorData: Record<string, unknown> = {};
			try {
				const parsed = await response.json();
				if (parsed && typeof parsed === "object" && !Array.isArray(parsed)) {
					errorData = parsed as Record<string, unknown>;
				}
			} catch {}
			const message =
				typeof errorData.message === "string"
					? errorData.message
					: `HTTP ${response.status}`;
			const code =
				typeof errorData.code === "string" ? errorData.code : undefined;
			throw new ApiError(message, response.status, code, errorData);
		}
		return response.json() as Promise<T>;
	}

	/**
	 * Check if error is retryable
	 * @param error - Error to check
	 * @returns True if error should be retried
	 */
	private isRetryableError(error: any): boolean {
		if (error instanceof ApiError) {
			// Retry on 5xx errors and rate limiting
			return error.status >= 500 || error.status === 429;
		}
		// Retry on network errors
		return error.name === "TypeError" && error.message.includes("fetch");
	}

	/**
	 * Get data from cache
	 * @param key - Cache key
	 * @returns Cached data or null
	 */
	private getFromCache(key: string): any | null {
		const cached = this.cache.get(key);
		if (!cached) return null;

		const now = Date.now();
		if (now - cached.timestamp > cached.ttl * 1000) {
			this.cache.delete(key);
			return null;
		}

		return cached.data;
	}

	/**
	 * Set data in cache
	 * @param key - Cache key
	 * @param data - Data to cache
	 * @param ttl - Time to live in seconds
	 */
	private setCache(key: string, data: any, ttl: number): void {
		this.cache.set(key, {
			data,
			timestamp: Date.now(),
			ttl,
		});

		// Clean up old cache entries periodically
		if (this.cache.size > 1000) {
			this.cleanupCache();
		}
	}

	/**
	 * Clean up expired cache entries
	 */
	private cleanupCache(): void {
		const now = Date.now();
		for (const [key, value] of this.cache.entries()) {
			if (now - value.timestamp > value.ttl * 1000) {
				this.cache.delete(key);
			}
		}
	}

	/**
	 * Create unique request key for deduplication
	 * @param method - HTTP method
	 * @param path - Request path
	 * @param params - Query parameters
	 * @param data - Request body
	 * @returns Unique request key
	 */
	private createRequestKey(
		method: string,
		path: string,
		params?: any,
		data?: any,
	): string {
		return `${method}:${path}:${JSON.stringify(params || {})}:${JSON.stringify(data || {})}`;
	}

	/**
	 * Sleep utility for retry delays
	 * @param ms - Milliseconds to sleep
	 * @returns Promise that resolves after delay
	 */
	private sleep(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * Clear all cached data
	 */
	clearCache(): void {
		this.cache.clear();
	}

	/**
	 * Get cache statistics
	 * @returns Cache statistics
	 */
	getCacheStats(): { size: number; keys: string[] } {
		return {
			size: this.cache.size,
			keys: Array.from(this.cache.keys()),
		};
	}
}
